{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/capstone-helper/app/users/page.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport { UserType } from \"@/store/users\";\r\n\r\nexport default async function Users() {\r\n  const res = await fetch(\"http://localhost:3000/api/users\");\r\n  const users: UserType[] = await res.json();\r\n\r\n  console.log(users);\r\n  return (\r\n    <div className=\"h-screen mt-20 container mx-auto gap-4\">\r\n      <h1 className=\"text-5xl font-bold mb-10\">Users</h1>\r\n      <div className=\"flex flex-row gap-4 flex-wrap w-full \">\r\n        {users?.map((user) => (\r\n          <div\r\n            key={user.id}\r\n            className=\"bg-white rounded-lg shadow-lg p-6 w-1/4 h-96\"\r\n          >\r\n            <h2 className=\"text-xl font-bold mb-4\">{user.name}</h2>\r\n            <p className=\"text-gray-600 mb-4\">{user.email}</p>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAGe,eAAe;IAC5B,MAAM,MAAM,MAAM,MAAM;IACxB,MAAM,QAAoB,MAAM,IAAI,IAAI;IAExC,QAAQ,GAAG,CAAC;IACZ,qBACE,8SAAC;QAAI,WAAU;;0BACb,8SAAC;gBAAG,WAAU;0BAA2B;;;;;;0BACzC,8SAAC;gBAAI,WAAU;0BACZ,OAAO,IAAI,CAAC,qBACX,8SAAC;wBAEC,WAAU;;0CAEV,8SAAC;gCAAG,WAAU;0CAA0B,KAAK,IAAI;;;;;;0CACjD,8SAAC;gCAAE,WAAU;0CAAsB,KAAK,KAAK;;;;;;;uBAJxC,KAAK,EAAE;;;;;;;;;;;;;;;;AAUxB", "debugId": null}}]}