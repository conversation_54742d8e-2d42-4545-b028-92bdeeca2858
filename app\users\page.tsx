import React from "react";
import { UserType } from "@/lib/users-storage";
import Link from "next/link";
import LinkButton from "@/components/ui/LinkButton";
import PageTitle from "@/components/ui/PageTitle";
import UserCard from "@/components/UserCard";

export default async function Users() {
  const res = await fetch("http://localhost:3000/api/users", {
    cache: "no-store", // Ensure fresh data on every request
  });
  const users: UserType[] = await res.json();

  if (!users) {
    return <div>No users found</div>;
  }

  return (
    <div className="h-screen mt-20 container mx-auto gap-4 max-w-2xl">
      <div className="flex flex-row justify-between items-center mb-10">
        <PageTitle>Users</PageTitle>
        <div className="flex flex-row gap-4">
          <LinkButton href="/users/add">Add User</LinkButton>
          <LinkButton href="/">Back Home</LinkButton>
        </div>
      </div>

      <div className="flex flex-row gap-4 flex-wrap w-full ">
        {users.map((user) => (
          <Link
            key={user.id}
            href={`/users/${user.id}
          `}
            className="w-full"
          >
            <UserCard user={user} />
          </Link>
        ))}
      </div>
    </div>
  );
}
