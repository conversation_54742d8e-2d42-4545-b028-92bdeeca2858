import { NextRequest, NextResponse } from "next/server";
import users from "@/store/users";
import { revalidatePath } from "next/cache";
import { UserType } from "@/store/users";

export async function GET(request: NextRequest) {
  return NextResponse.json(users);
}

export async function POST(request: NextRequest) {
  try {
    // Parse FormData from the request
    const formData = await request.formData();

    // Extract the form fields
    const name = formData.get("name") as string;
    const email = formData.get("email") as string;
    const role = formData.get("role") as string;

    console.log("name", name);
    console.log("email", email);
    console.log("role", role);

    // Validate required fields
    if (!name || !email || !role) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Create new user object
    const newUser: UserType = {
      id: users.length + 1, // Simple ID generation
      name,
      email,
      role,
    };

    // Add to users array
    users.push(newUser);

    // Revalidate the users page cache
    revalidatePath("/users");

    return NextResponse.json(newUser, { status: 201 });
    console.log()
  } catch (error) {
    console.error("Error creating user:", error);
    return NextResponse.json(
      { error: "Failed to create user" },
      { status: 500 }
    );
  }
}
