import { NextRequest, NextResponse } from "next/server";
import users from "@/store/users";
import { revalidatePath } from "next/cache";
import { UserType } from "@/store/users";

export async function GET(request: NextRequest) {
  return NextResponse.json(users);
}

export async function POST(request: NextRequest, formData: FormData) {
  console.log("formData", formData);
  console.log(request);
  const newUser: UserType = {
    id: users.length + 1,
    name: formData.get("name") as string,
    email: formData.get("email") as string,
    role: formData.get("role") as string,
  };
  console.log("newUser", newUser);
  //const newUser = await request.json();
  //users.push(newUser);
  //revalidatePath("/users");
  //return NextResponse.json(newUser);
}
