import { NextRequest, NextResponse } from "next/server";
import { revalidatePath } from "next/cache";
import users, { UserType } from "@/store/users";

export async function GET() {
  return NextResponse.json(users);
}

export async function POST(request: NextRequest) {
  try {
    // Parse FormData from the request
    const formData = await request.formData();

    // Extract the form fields
    const name = formData.get("name") as string;
    const email = formData.get("email") as string;
    const role = formData.get("role") as string;

    // Validate required fields
    if (!name || !email || !role) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Generate new ID based on existing users
    const maxId = users.length > 0 ? Math.max(...users.map((u) => u.id)) : 0;
    const newUser: UserType = {
      id: maxId + 1,
      name,
      email,
      role,
    };

    // Add to in-memory users array
    users.push(newUser);

    // Revalidate the users page cache
    revalidatePath("/users");

    return NextResponse.json(newUser, { status: 201 });
  } catch (error) {
    console.error("Error creating user:", error);
    return NextResponse.json(
      { error: "Failed to create user" },
      { status: 500 }
    );
  }
}
