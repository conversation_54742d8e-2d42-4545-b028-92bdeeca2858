import { NextRequest, NextResponse } from "next/server";
import { revalidatePath } from "next/cache";
import { getAllUsers, addUser } from "@/lib/users-storage";

export async function GET() {
  const users = getAllUsers();
  return NextResponse.json(users);
}

export async function POST(request: NextRequest) {
  try {
    // Parse FormData from the request
    const formData = await request.formData();

    // Extract the form fields
    const name = formData.get("name") as string;
    const email = formData.get("email") as string;
    const role = formData.get("role") as string;

    // Validate required fields
    if (!name || !email || !role) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Add user using the service
    const newUser = addUser({
      name,
      email,
      role,
    });

    // Revalidate the users page cache
    revalidatePath("/users");

    return NextResponse.json(newUser, { status: 201 });
  } catch (error) {
    console.error("Error creating user:", error);
    return NextResponse.json(
      { error: "Failed to create user" },
      { status: 500 }
    );
  }
}
