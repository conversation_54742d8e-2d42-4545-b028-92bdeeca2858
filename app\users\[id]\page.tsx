import React from "react";
import { UserType } from "@/lib/users-storage";
import { notFound } from "next/navigation";

import DeleteUserButton from "@/components/ui/DeleteUserButton";
import Link from "next/link";
import PageTitle from "@/components/ui/PageTitle";
import LinkButton from "@/components/ui/LinkButton";
import CardContainer from "@/components/ui/CardContainer";

interface UserPageProps {
  params: Promise<{ id: string }>;
}

export default async function UserPage({ params }: UserPageProps) {
  // Await params before accessing its properties
  const { id } = await params;

  try {
    const res = await fetch(`http://localhost:3000/api/users/${id}`);

    if (!res.ok) {
      notFound();
    }

    const user: UserType = await res.json();

    return (
      <div className="h-screen mt-20 container mx-auto max-w-2xl">
        <div className="max-w-2xl mx-auto">
          <div className="mb-10 flex justify-between items-center gap-4">
            <PageTitle>User Details</PageTitle>
            <div>
              <LinkButton href="/users">Back to Users</LinkButton>
            </div>
          </div>
          <CardContainer>
            <div className="mb-6 w-full">
              <h1 className="text-4xl font-bold text-gray-100 mb-2">
                {user.name}
              </h1>
              <p className="text-lg text-gray-400">{user.email}</p>
            </div>

            <div className="space-y-4 w-full">
              <div className="w-full flex flex-row gap-4">
                <label className="block  font-medium text-gray-100 mb-1 text-xl font-bold">
                  User ID:
                </label>
                <p className="text-gray-100 text-xl ">{user.id}</p>
              </div>

              <div className="w-full flex flex-row gap-4 items-center">
                <label className="block text-xl font-bold text-gray-100 mb-1">
                  Role:
                </label>
                <span
                  className={`inline-flex px-3 py-1 rounded-full text-xl font-medium ${
                    user.role === "admin"
                      ? "bg-red-100 text-red-800"
                      : "bg-blue-100 text-blue-800"
                  }`}
                >
                  {user.role}
                </span>
              </div>
              <div className="flex flex-row gap-4 justify-end">
                <DeleteUserButton userId={user.id} />
                <LinkButton href={`/users/${user.id}/edit`} color="blue">
                  Edit
                </LinkButton>
              </div>
            </div>
          </CardContainer>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error fetching user:", error);
    notFound();
  }
}
