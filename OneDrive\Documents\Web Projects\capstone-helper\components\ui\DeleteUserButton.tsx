"use client";

import React from "react";

import { redirect } from "next/navigation";

type DeleteUserButtonProps = {
  userId: number;
};

export default function DeleteUserButton({ userId }: DeleteUserButtonProps) {
  const handleDelete = async () => {
    try {
      const res = await fetch(`http://localhost:3000/api/users/${userId}`, {
        method: "DELETE",
      });

      if (!res.ok) {
        throw new Error("Failed to delete user");
      } else {
        redirect("/users");
      }
    } catch (error) {
      console.error("Error deleting user:", error);
    }
  };

  return (
    <button
      onClick={handleDelete}
      className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg"
    >
      Delete
    </button>
  );
}
