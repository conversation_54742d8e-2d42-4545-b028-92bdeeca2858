"use client";

import React from "react";

import { useRouter } from "next/navigation";

type DeleteUserButtonProps = {
  userId: number;
};

export default function DeleteUserButton({ userId }: DeleteUserButtonProps) {
  const router = useRouter();

  const handleDelete = async () => {
    try {
      console.log("Deleting user:", userId);
      const res = await fetch(`http://localhost:3000/api/users/${userId}`, {
        method: "DELETE",
      });

      if (!res.ok) {
        throw new Error("Failed to delete user");
      } else {
        console.log("User deleted successfully, navigating...");
        // Navigate back to users page and force a refresh
        router.push("/users");
        // Small delay to ensure navigation happens
        setTimeout(() => {
          router.refresh();
        }, 100);
      }
    } catch (error) {
      console.error("Error deleting user:", error);
    }
  };

  return (
    <button
      onClick={handleDelete}
      className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg"
    >
      Delete
    </button>
  );
}
