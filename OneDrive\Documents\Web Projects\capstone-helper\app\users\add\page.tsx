"use client";

import { redirect } from "next/navigation";
import Link from "next/link";
import React, { FormEvent } from "react";

export default function AddUser() {
  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);

    if (
      !formData.get("name") ||
      !formData.get("email") ||
      !formData.get("role")
    ) {
      alert("Please fill out all fields");
      return;
    }

    const res = await fetch("http://localhost:3000/api/users", {
      method: "POST",
      body: formData,
    });

    if (!res.ok) {
      throw new Error("Failed to add user");
    } else {
      redirect("/users");
    }
  };

  return (
    <div className="h-screen mt-20 container mx-auto max-w-2xl flex-col gap-4">
      <div className="flex flex-row justify-between items-center mb-10">
        <h1 className="text-5xl font-bold ">Add User</h1>
        <Link
          href="/users"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          ← Back to Users
        </Link>
      </div>

      <form onSubmit={handleSubmit} className="flex flex-col gap-4">
        <div className="flex flex-col gap-2">
          <label htmlFor="name">Name</label>
          <input
            type="text"
            name="name"
            className="border border-gray-300 rounded-lg p-2 bg-zinc-700"
          />
        </div>

        <div className="flex flex-col gap-2">
          <label htmlFor="email">Email</label>
          <input
            type="email"
            name="email"
            className="border border-gray-300 rounded-lg p-2 bg-zinc-700"
          />
        </div>
        <div className="flex flex-col gap-2">
          <label htmlFor="role">Role</label>
          <input
            type="text"
            name="role"
            className="border border-gray-300 rounded-lg p-2 bg-zinc-700"
          />
        </div>
        <div className="mt-10">
          <button
            type="submit"
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
          >
            Submit
          </button>
        </div>
      </form>
    </div>
  );
}
