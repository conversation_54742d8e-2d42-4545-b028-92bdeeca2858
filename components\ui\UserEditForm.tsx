"use client";

import React from "react";
import <PERSON> from "next/link";
import { useRouter } from "next/navigation";
import { UserType } from "@/lib/users-storage";
import LinkButton from "./LinkButton";

import Input from "./Input";
import Button from "./Button";

interface UserEditFormProps {
  user: UserType;
  id: string;
}

export default function UserEditForm({ user, id }: UserEditFormProps) {
  const router = useRouter();

  const updateUser = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);

    // Validate required fields
    const name = formData.get("name") as string;
    const email = formData.get("email") as string;
    const role = formData.get("role") as string;

    if (!name?.trim() || !email?.trim() || !role?.trim()) {
      alert("Please fill out all fields");
      return;
    }

    try {
      const res = await fetch(`http://localhost:3000/api/users/${id}`, {
        method: "PUT",
        body: formData,
      });

      if (!res.ok) {
        throw new Error("Failed to update user");
      }

      // Navigate back to users list and refresh
      router.push("/users");
      setTimeout(() => {
        router.refresh();
      }, 100);
    } catch (error) {
      console.error("Error updating user:", error);
      alert("Failed to update user. Please try again.");
    }
  };

  return (
    <form onSubmit={updateUser} className="flex flex-col gap-4">
      <Input name="Name" type="text" defaultValue={user.name} />

      <Input name="Email" type="email" defaultValue={user.email} />

      <select
        name="role"
        defaultValue={user.role}
        className="border border-gray-300 rounded-lg p-2 bg-zinc-700"
      >
        <option value="admin">Admin</option>
        <option value="user">User</option>
      </select>

      <div className="mt-6 flex flex-row gap-4">
        <Button type="submit">Update User</Button>
        <LinkButton href={`/users/${id}`}>Cancel</LinkButton>
      </div>
    </form>
  );
}
