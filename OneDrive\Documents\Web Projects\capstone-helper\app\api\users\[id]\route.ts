import { NextRequest, NextResponse } from "next/server";
import { revalidatePath } from "next/cache";
import { getUserById, deleteUser, updateUser } from "@/lib/users-storage";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const userId = parseInt(id);

  console.log("Looking for user ID:", userId);

  const user = getUserById(userId);

  if (!user) {
    return NextResponse.json({ error: "User not found" }, { status: 404 });
  }

  return NextResponse.json(user);
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const userId = parseInt(id);

  console.log("Deleting user ID:", userId);

  const success = deleteUser(userId);

  if (!success) {
    return NextResponse.json({ error: "User not found" }, { status: 404 });
  }

  // Revalidate the users page cache
  revalidatePath("/users");

  return NextResponse.json({ message: "User deleted successfully" });
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const userId = parseInt(id);

  console.log("Updating user ID:", userId);

  const formData = await request.formData();

  const name = formData.get("name") as string;
  const email = formData.get("email") as string;
  const role = formData.get("role") as string;
  const updatedUser = updateUser(userId, { name, email, role });

  if (!updatedUser) {
    return NextResponse.json({ error: "User not found" }, { status: 404 });
  }

  // Revalidate the users page cache
  revalidatePath("/users");

  return NextResponse.json(updatedUser);
}
