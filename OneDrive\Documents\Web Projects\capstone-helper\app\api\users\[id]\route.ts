import { NextRequest, NextResponse } from "next/server";
import { revalidatePath } from "next/cache";
import { getUserById, deleteUser } from "@/lib/users-storage";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const userId = parseInt(id);

  console.log("Looking for user ID:", userId);

  const user = getUserById(userId);

  if (!user) {
    return NextResponse.json({ error: "User not found" }, { status: 404 });
  }

  return NextResponse.json(user);
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const userId = parseInt(id);

  console.log("Deleting user ID:", userId);

  const success = deleteUser(userId);

  if (!success) {
    return NextResponse.json({ error: "User not found" }, { status: 404 });
  }

  // Revalidate the users page cache
  revalidatePath("/users");

  return NextResponse.json({ message: "User deleted successfully" });
}
