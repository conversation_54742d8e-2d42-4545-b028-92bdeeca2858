"use client";
import CardContainer from "@/components/ui/CardContainer";
import React, { useEffect } from "react";

type ClientContainerProps = {
  children: React.ReactNode;
};
export default function ClientContainer({ children }: ClientContainerProps) {
  const [color, setColor] = React.useState<string>("zinc-900");

  useEffect(() => {
    const randomColor = () => {
      const letters = "0123456789ABCDEF";
      let color = "#";
      for (let i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)];
      }
      return color;
    };

    const interval = setInterval(() => {
      setColor(randomColor());
    }, 3000);

    return () => {
      clearInterval(interval);
    };
  }, [color]);

  return (
    <CardContainer
      style={{ backgroundColor: color }}
      className="transition-all duration-3000 ease-in-out"
    >
      {children}
    </CardContainer>
  );
}
