import React from "react";

type ButtonProps = {
  children: React.ReactNode;
  color?: "red" | "blue" | "gray";
  type?: "button" | "submit" | "reset";
};

export default function Button({
  children,
  type,
  color = "blue",
}: ButtonProps) {
  return (
    <button
      type={type}
      className={`bg-${color}-500 hover:bg-${color}-900 text-white px-4 py-2 rounded-lg text-xl font-bold transition-colors`}
    >
      {children}
    </button>
  );
}
