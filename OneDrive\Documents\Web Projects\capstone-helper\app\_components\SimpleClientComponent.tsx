"use client";

import React, { useEffect } from "react";

export default function SimpleClientComponent() {
  const [text, setText] = React.useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await fetch(`/api/hi`);
        const data = await res.json();
        setText(data.message);
      } catch (error) {
        console.log(error);
      }
    };

    fetchData();
  }, []);

  return (
    <>
      <h1 className="text-2xl font-bold mb-4">Client API Response:</h1>
      <p className="text-lg">{text ? text : "Loading..."}</p>
    </>
  );
}
