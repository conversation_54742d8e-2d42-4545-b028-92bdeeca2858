import React from "react";
import Link from "next/link";

type LinkButtonProps = {
  href: string;
  children: React.ReactNode;
  color?: "blue" | "zinc";
};

export default function LinkButton({
  href,
  children,
  color = "zinc",
}: LinkButtonProps) {
  const colorClass =
    color === "zinc"
      ? `bg-zinc-100 hover:bg-zinc-700`
      : `bg-blue-500 hover:bg-900 blue-700 text-white`;

  return (
    <Link
      href={href}
      className={`${colorClass} text-black hover:text-white px-4 py-2 rounded-lg duration-300 transition-all text-xl font-bold flex items-center gap-2 border border-transparent hover:border-zinc-600`}
    >
      {children}
    </Link>
  );
}
