import React from "react";

type CardContainerProps = {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
};

export default function CardContainer({
  children,
  style,
  className,
}: CardContainerProps) {
  return (
    <div
      className={`flex flex-col items-center justify-center bg-zinc-900 p-4 rounded-lg border border-zinc-700 flex-1 ${className}`}
      style={style}
    >
      {children}
    </div>
  );
}
