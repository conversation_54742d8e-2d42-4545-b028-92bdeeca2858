import Link from "next/link";
import Card<PERSON>ontainer from "@/components/ui/CardContainer";
import SimpleClientComponent from "./_components/SimpleClientComponent";
import ClientContainer from "./_components/ClientContainer";
import StillServerComponent from "./_components/StillServerComponent";
import LinkButton from "@/components/ui/LinkButton";
import PageTitle from "@/components/ui/PageTitle";

export default async function Home() {
  const res = await fetch(`http://localhost:3000/api/hello`);
  const data = await res.json();

  return (
    <div className="h-screen mt-20 container mx-auto gap-4 max-w-2xl">
      <div className="mb-10 flex justify-between items-center ">
        <PageTitle>Home</PageTitle>
        <div className="flex flex-row gap-4">
          <LinkButton href="/other-page">Other Page</LinkButton>
          <LinkButton href="/users">Users</LinkButton>
        </div>
      </div>

      <div className="flex flex-row gap-4 flex-wrap w-full ">
        <CardContainer>
          <h1 className="text-2xl font-bold mb-4">Server API Response:</h1>
          <p className="text-lg">{data.message}</p>
        </CardContainer>
        <CardContainer>
          <SimpleClientComponent />
        </CardContainer>

        <ClientContainer>
          <StillServerComponent />
        </ClientContainer>
      </div>
    </div>
  );
}
