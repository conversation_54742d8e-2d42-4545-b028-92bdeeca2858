import React from "react";
import { UserType } from "@/store/users";
import { notFound } from "next/navigation";

interface UserPageProps {
  params: Promise<{ id: string }>;
}

export default async function UserPage({ params }: UserPageProps) {
  // Await params before accessing its properties
  const { id } = await params;

  try {
    const res = await fetch(`http://localhost:3000/api/users/${id}`);

    if (!res.ok) {
      notFound();
    }

    const user: UserType = await res.json();

    return (
      <div className="h-screen mt-20 container mx-auto max-w-2xl">
        <div className="max-w-2xl mx-auto">
          <div className="bg-zinc-700 rounded-lg shadow-lg p-8">
            <div className="mb-6">
              <h1 className="text-4xl font-bold text-gray-100 mb-2">
                {user.name}
              </h1>
              <p className="text-lg text-gray-500">{user.email}</p>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-100 mb-1">
                  User ID
                </label>
                <p className="text-gray-100">{user.id}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-100 mb-1">
                  Role
                </label>
                <span
                  className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${
                    user.role === "admin"
                      ? "bg-red-100 text-red-800"
                      : "bg-blue-100 text-blue-800"
                  }`}
                >
                  {user.role}
                </span>
              </div>
            </div>

            <div className="mt-8">
              <a
                href="/users"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                ← Back to Users
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error fetching user:", error);
    notFound();
  }
}
