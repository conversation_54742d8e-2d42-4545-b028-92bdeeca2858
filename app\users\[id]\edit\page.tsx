import React from "react";
import LinkButton from "@/components/ui/LinkButton";
import UserEditForm from "@/components/ui/UserEditForm";
import PageTitle from "@/components/ui/PageTitle";

interface UserPageProps {
  params: Promise<{ id: string }>;
}

export default async function EditUser({ params }: UserPageProps) {
  const { id } = await params;
  const res = await fetch(`http://localhost:3000/api/users/${id}`);
  const user = await res.json();

  return (
    <>
      <div className="h-screen mt-20 container mx-auto gap-4 max-w-2xl flex-col gap-4">
        <div className="flex flex-row justify-between items-center mb-10">
          <PageTitle>Edit User</PageTitle>
          <div className="flex flex-row gap-4">
            <LinkButton href={`/users/${id}`}>Back to User</LinkButton>
            <LinkButton href="/users">Back to Users</LinkButton>
          </div>
        </div>
        <UserEditForm user={user} id={id} />
      </div>
    </>
  );
}
