import React from "react";
import { updateUser, UserType } from "@/lib/users-storage";
import Link from "next/link";
import { redirect } from "next/navigation";
import UserForm from "@/components/ui/UserForm";

interface UserPageProps {
  params: Promise<{ id: string }>;
}

export default async function EditUser({ params }: UserPageProps) {
  const { id } = await params;
  const res = await fetch(`http://localhost:3000/api/users/${id}`);
  const user = await res.json();

  return (
    <>
      <div className="h-screen mt-20 container mx-auto gap-4 max-w-2xl flex-col gap-4">
        <div className="flex flex-row justify-between items-center mb-10">
          <h1 className="text-5xl font-bold ">User Edit</h1>
          <div className="flex flex-row gap-4">
            <Link
              href={`/users/${id}`}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Back to User
            </Link>
            <Link
              href="/users"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Back to Users
            </Link>
          </div>
        </div>
        <UserForm user={user} id={id} />
      </div>
    </>
  );
}
