export type UserType = {
  id: number;
  name: string;
  email: string;
  role: string;
};

// In-memory users storage - this will be shared across all API routes
let users: UserType[] = [
  {
    id: 1,
    name: "<PERSON> Sagg<PERSON>",
    email: "<EMAIL>",
    role: "admin",
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "user",
  },
  {
    id: 3,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "user",
  },
];

export function getAllUsers(): UserType[] {
  return users;
}

export function getUserById(id: number): UserType | undefined {
  return users.find((user) => user.id === id);
}

export function addUser(userData: Omit<UserType, "id">): UserType {
  // Generate new ID based on existing users
  const maxId = users.length > 0 ? Math.max(...users.map((u) => u.id)) : 0;
  const newUser: UserType = {
    id: maxId + 1,
    ...userData,
  };

  users.push(newUser);
  return newUser;
}

export function deleteUser(id: number): boolean {
  const index = users.findIndex((user) => user.id === id);
  if (index !== -1) {
    users.splice(index, 1);
    return true;
  }
  return false;
}

export function updateUser(
  id: number,
  userData: Partial<Omit<UserType, "id">>
): UserType | null {
  const userIndex = users.findIndex((user) => user.id === id);
  if (userIndex !== -1) {
    users[userIndex] = { ...users[userIndex], ...userData };
    return users[userIndex];
  }
  return null;
}
