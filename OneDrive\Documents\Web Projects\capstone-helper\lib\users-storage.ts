import fs from 'fs';
import path from 'path';
import { UserType } from '@/store/users';

const USERS_FILE_PATH = path.join(process.cwd(), 'data', 'users.json');

// Ensure data directory exists
function ensureDataDirectory() {
  const dataDir = path.dirname(USERS_FILE_PATH);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
}

// Read users from file
export function readUsersFromFile(): UserType[] {
  try {
    ensureDataDirectory();
    
    if (!fs.existsSync(USERS_FILE_PATH)) {
      // If file doesn't exist, create it with default users
      const defaultUsers: UserType[] = [
        {
          id: 1,
          name: "Bob Sagget",
          email: "<EMAIL>",
          role: "admin",
        },
        {
          id: 2,
          name: "<PERSON>",
          email: "<EMAIL>",
          role: "user",
        },
        {
          id: 3,
          name: "<PERSON>",
          email: "<EMAIL>",
          role: "user",
        },
      ];
      writeUsersToFile(defaultUsers);
      return defaultUsers;
    }
    
    const fileContent = fs.readFileSync(USERS_FILE_PATH, 'utf-8');
    return JSON.parse(fileContent);
  } catch (error) {
    console.error('Error reading users from file:', error);
    return [];
  }
}

// Write users to file
export function writeUsersToFile(users: UserType[]): void {
  try {
    ensureDataDirectory();
    fs.writeFileSync(USERS_FILE_PATH, JSON.stringify(users, null, 2));
  } catch (error) {
    console.error('Error writing users to file:', error);
  }
}

// Add a new user and save to file
export function addUser(user: UserType): UserType {
  const users = readUsersFromFile();
  
  // Generate new ID
  const maxId = users.length > 0 ? Math.max(...users.map(u => u.id)) : 0;
  const newUser = { ...user, id: maxId + 1 };
  
  users.push(newUser);
  writeUsersToFile(users);
  
  return newUser;
}

// Get user by ID
export function getUserById(id: number): UserType | undefined {
  const users = readUsersFromFile();
  return users.find(user => user.id === id);
}

// Get all users
export function getAllUsers(): UserType[] {
  return readUsersFromFile();
}
