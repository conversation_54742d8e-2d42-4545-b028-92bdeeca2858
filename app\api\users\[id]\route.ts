import { NextRequest, NextResponse } from "next/server";
import { revalidatePath } from "next/cache";
import { getUserById, deleteUser, updateUser } from "@/lib/users-storage";

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const userId = parseInt(id);
  const user = getUserById(userId);

  if (!user) {
    return NextResponse.json({ error: "User not found" }, { status: 404 });
  }

  return NextResponse.json(user);
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const userId = parseInt(id);
  const success = deleteUser(userId);

  if (!success) {
    return NextResponse.json({ error: "User not found" }, { status: 404 });
  }

  // Revalidate the users page cache
  revalidatePath("/users");

  return NextResponse.json({ message: "User deleted successfully" });
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const userId = parseInt(id);
  const formData = await request.formData();

  const name = formData.get("name") as string;
  const email = formData.get("email") as string;
  const role = formData.get("role") as string;

  // Validate required fields
  if (!name?.trim() || !email?.trim() || !role?.trim()) {
    return NextResponse.json(
      { error: "Missing required fields" },
      { status: 400 }
    );
  }

  const updatedUser = updateUser(userId, { name, email, role });

  if (!updatedUser) {
    return NextResponse.json({ error: "User not found" }, { status: 404 });
  }

  // Revalidate the users page cache and individual user page
  revalidatePath("/users");
  revalidatePath(`/users/${userId}`);

  return NextResponse.json(updatedUser);
}
