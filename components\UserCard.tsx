import React from "react";
import { UserType } from "@/lib/users-storage";

type UserCardProps = {
  user: UserType;
};

export default function UserCard({ user }: UserCardProps) {
  return (
    <div className="bg-zinc-700 rounded-lg shadow-lg p-6 w-full  hover:shadow-xl hover:scale-103  duration-300 transition-all cursor-pointer">
      <h2 className="text-2xl font-bold text-white-900">{user.name}</h2>
      <p className="text-gray-400 mb-4">{user.email}</p>
      <div className="mt-auto">
        <span
          className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${
            user.role === "admin"
              ? "bg-red-100 text-red-800"
              : "bg-blue-100 text-blue-800"
          }`}
        >
          {user.role}
        </span>
      </div>
    </div>
  );
}
