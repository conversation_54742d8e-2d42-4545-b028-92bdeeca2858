import React from "react";

type InputProps = {
  name: string;
  type: string;
  defaultValue?: string;
};

export default function Input({ name, type, defaultValue }: InputProps) {
  return (
    <div className="flex flex-col gap-2">
      <label htmlFor={name.toLocaleLowerCase()}>
        {name.charAt(0).toUpperCase() + name.slice(1)}
      </label>
      <input
        type={type}
        defaultValue={defaultValue ? defaultValue : ""}
        name={name.toLocaleLowerCase()}
        className="border border-gray-300 rounded-lg p-2 bg-zinc-700"
        required
      />
    </div>
  );
}
