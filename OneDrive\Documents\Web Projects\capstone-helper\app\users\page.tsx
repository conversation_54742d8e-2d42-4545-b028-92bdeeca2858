import React from "react";
import { UserType } from "@/lib/users-storage";
import Link from "next/link";

export default async function Users() {
  const res = await fetch("http://localhost:3000/api/users");
  const users: UserType[] = await res.json();

  if (!users) {
    return <div>No users found</div>;
  }

  return (
    <div className="h-screen mt-20 container mx-auto gap-4 max-w-2xl">
      <div className="flex flex-row justify-between items-center mb-10">
        <h1 className="text-5xl font-bold ">Users</h1>
        <div className="flex flex-row gap-4">
          <Link
            href="/users/add"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Add User
          </Link>
          <Link
            href="/"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Back Home
          </Link>
        </div>
      </div>

      <div className="flex flex-row gap-4 flex-wrap w-full ">
        {users.map((user) => (
          <Link
            key={user.id}
            href={`/users/${user.id}`}
            className="bg-zinc-700 rounded-lg shadow-lg p-6 w-full  hover:shadow-xl transition-shadow cursor-pointer"
          >
            <h2 className="text-2xl font-bold text-white-900">{user.name}</h2>
            <p className="text-gray-400 mb-4">{user.email}</p>
            <div className="mt-auto">
              <span
                className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${
                  user.role === "admin"
                    ? "bg-red-100 text-red-800"
                    : "bg-blue-100 text-blue-800"
                }`}
              >
                {user.role}
              </span>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}
