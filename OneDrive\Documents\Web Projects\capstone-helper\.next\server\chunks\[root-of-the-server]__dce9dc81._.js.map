{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/capstone-helper/store/users.ts"], "sourcesContent": ["export type UserType = {\r\n  id: number;\r\n  name: string;\r\n  email: string;\r\n  role: string;\r\n};\r\n\r\nconst users: UserType[] = [\r\n  {\r\n    id: 1,\r\n    name: \"<PERSON>\",\r\n    email: \"<EMAIL>\",\r\n    role: \"admin\",\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"<PERSON>\",\r\n    email: \"<EMAIL>\",\r\n    role: \"user\",\r\n  },\r\n  {\r\n    id: 3,\r\n    name: \"<PERSON>\",\r\n    email: \"<EMAIL>\",\r\n    role: \"user\",\r\n  },\r\n];\r\n\r\nexport default users;\r\n"], "names": [], "mappings": ";;;;AAOA,MAAM,QAAoB;IACxB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;IACR;CACD;uCAEc", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/capstone-helper/app/api/users/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\r\nimport users from \"@/store/users\";\r\n\r\nexport async function GET(request: NextRequest) {\r\n  console.log(users);\r\n  return NextResponse.json(users);\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,QAAQ,GAAG,CAAC,2LAAK;IACjB,OAAO,gNAAY,CAAC,IAAI,CAAC,2LAAK;AAChC", "debugId": null}}]}