"use client";

import { redirect } from "next/navigation";
import Link from "next/link";
import React, { FormEvent } from "react";
import LinkButton from "@/components/ui/LinkButton";
import PageTitle from "@/components/ui/PageTitle";
import UserAddForm from "@/components/ui/UserAddForm";

export default function AddUser() {
  return (
    <div className="h-screen mt-20 container mx-auto max-w-2xl flex-col gap-4">
      <div className="flex flex-row justify-between items-center mb-10">
        <PageTitle>Add User</PageTitle>
        <LinkButton href="/users">Back to Users</LinkButton>
      </div>

      <UserAddForm />
    </div>
  );
}
