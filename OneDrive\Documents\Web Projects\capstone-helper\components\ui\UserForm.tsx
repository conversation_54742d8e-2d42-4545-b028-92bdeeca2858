"use client";

import React from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";

import { UserType } from "@/lib/users-storage";

type UserFormProps = {
  user: UserType;
  id: string;
};

export default function UserForm({ user, id }: UserFormProps) {
  const updateUser = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);

    if (
      !formData.get("name") ||
      !formData.get("email") ||
      !formData.get("role")
    ) {
      alert("Please fill out all fields");
      return;
    }

    console.log("Updating user:", id);
    const res = await fetch(`http://localhost:3000/api/users/${id}`, {
      method: "PUT",
      body: formData,
    });

    if (!res.ok) {
      throw new Error("Failed to update user");
    } else {
      console.log("User updated successfully, navigating...");
      // Force a full page reload to ensure fresh data
      window.location.href = "/users";
    }
  };

  return (
    <div>
      <form onSubmit={updateUser}>
        <div className="flex flex-col gap-2">
          <label htmlFor="name">Name</label>
          <input
            type="text"
            name="name"
            defaultValue={user.name}
            className="border border-gray-300 rounded-lg p-2 bg-zinc-700"
          />
        </div>
        <div className="flex flex-col gap-2">
          <label htmlFor="email">Email</label>
          <input
            type="email"
            name="email"
            defaultValue={user.email}
            className="border border-gray-300 rounded-lg p-2 bg-zinc-700"
          />
        </div>
        <div className="flex flex-col gap-2">
          <label htmlFor="role">Role</label>
          <input
            type="text"
            name="role"
            defaultValue={user.role}
            className="border border-gray-300 rounded-lg p-2 bg-zinc-700"
          />
        </div>
        <div className="mt-10 flex flex-row gap-4">
          <button
            type="submit"
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
          >
            Submit
          </button>
          <Link
            href={`/users/${id}`}
            className="inline-flex ms-3items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </Link>
        </div>
      </form>
    </div>
  );
}
